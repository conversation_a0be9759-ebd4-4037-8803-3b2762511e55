<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>io.simpleit.devapp</groupId>
		<artifactId>parent</artifactId>
		<version>1.0.0</version>
	</parent>

	<artifactId>order-app</artifactId>
	<name>order-app</name>

	<dependencies>
		<dependency>
			<groupId>io.simpleit.devapp</groupId>
			<artifactId>common</artifactId>
			<version>1.0.0</version>
		</dependency>

		<!-- Configuration processor for IDE support -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<!-- Test dependencies -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<build>
		<resources>
			<resource>
				<directory>${project.basedir}/src/main/resources</directory>
				<filtering>false</filtering>
			</resource>
			<resource>
				<directory>${project.parent.basedir}/src/main/resources</directory>
				<filtering>false</filtering>
				<includes>
					<include>logback-spring.xml</include>
				</includes>
			</resource>
		</resources>
	</build>

</project>
